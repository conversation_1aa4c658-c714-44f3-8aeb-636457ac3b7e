import { FlatList, StyleSheet, Text, TextInput, View } from "react-native";
import React, { useState } from "react";
import { horizontalScale, verticalScale } from "../../../Utilities/Metrics";
import COLORS from "../../../Utilities/Colors";
import CustomIcon from "../../../Components/CustomIcon";
import ICONS from "../../../Assets/Icons";
import { ChatBubble } from "../../Plan/WorkoutProgramDetails";
import { KeyboardAvoidingContainer } from "../../../Components/KeyboardAvoidingComponent";
const messages = [
  { id: "1", text: "you will have to do hard", sender: false },
  { id: "2", text: "How do I work the bench press", sender: true },
  { id: "3", text: "You can start with light weights", sender: false },
];

const CoachCenterView = () => {
  const [message, setMessage] = useState("");

  return (
    <KeyboardAvoidingContainer backgroundColor="">
      <View style={styles.conversationContainer}>
        <FlatList
          data={messages}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <ChatBubble text={item.text} sender={item.sender} />
          )}
        />
        <View style={styles.inputContainer}>
          <TextInput
            value={message}
            onChangeText={setMessage}
            placeholder="Type a message..."
            style={styles.messageInput}
          />
          <View style={styles.sendIconContainer}>
            <CustomIcon Icon={ICONS.SendMessageIcon} height={40} width={40} />
          </View>
        </View>
      </View>
    </KeyboardAvoidingContainer>
  );
};

export default CoachCenterView;

const styles = StyleSheet.create({
  conversationContainer: {
    paddingHorizontal: horizontalScale(10),
    gap: verticalScale(10),
    flex: 1,
    justifyContent: "space-between",
    marginBottom: verticalScale(20),
  },
  chatBubble: {
    padding: 12,
    borderTopEndRadius: 16,
    borderBottomStartRadius: 16,
    maxWidth: "75%",
    marginVertical: 4,
  },
  chatBubbleText: {
    color: COLORS.black,
    fontSize: 16,
  },
  inputContainer: {
    position: "relative",
  },
  messageInput: {
    backgroundColor: COLORS.white,
    borderRadius: 100,
    paddingVertical: verticalScale(10),
    paddingHorizontal: horizontalScale(20),
    borderWidth: 1.5,
    borderColor: "#979C9E",
  },
  sendIconContainer: {
    position: "absolute",
    right: horizontalScale(0),
    top: "50%",
    transform: [{ translateY: -20 }],
  },
});
