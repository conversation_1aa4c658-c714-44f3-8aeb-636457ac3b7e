import React, { FC, useCallback, useEffect, useRef, useState } from "react";
import {
  Animated,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icons";
import CustomIcon from "../../Components/CustomIcon";
import { CustomText } from "../../Components/CustomText";
import PrimaryButton from "../../Components/PrimaryButton";
import { Ingredients } from "../../Seeds/MealPlansData";
import { LogMealScreenProps } from "../../Typings/route";
import COLORS from "../../Utilities/Colors";
import { horizontalScale, verticalScale, wp } from "../../Utilities/Metrics";

const LogMeal: FC<LogMealScreenProps> = ({ navigation }) => {
  const [currentIndex, setCurrentIndex] = useState(0); // Base index for percentage
  const [selectedIngredients, setSelectedIngredients] = useState<string[]>([]); // Array to store selected ingredient titles
  const [ingredientsData, setIngredientsData] = useState([...Ingredients]);

  const handleIncrease = () => {
    setCurrentIndex(currentIndex + 1);
  };

  const handleDecrease = () => {
    setCurrentIndex(currentIndex - 1);
  };

  const getPercentageValues = (index: number) => {
    const step = 10; // Step size for percentage change
    return {
      lower: index * step - step,
      current: index * step,
      upper: index * step + step,
    };
  };
  // For animation
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const deleteItemId = useRef<string | null>(null);

  // Calculate total macros
  const calculateTotalMacros = useCallback(() => {
    return ingredientsData.reduce(
      (totals, item: any) => {
        return {
          calories: totals.calories + item.calories,
          fat: totals.fat + item.fat,
          protein: totals.protein + item.protein,
          carbs: totals.carbs + item.carbs,
        };
      },
      { calories: 0, fat: 0, protein: 0, carbs: 0 }
    );
  }, [ingredientsData]);

  const [totalMacros, setTotalMacros] = useState(calculateTotalMacros());

  useEffect(() => {
    setTotalMacros(calculateTotalMacros());
  }, [ingredientsData, calculateTotalMacros]);

  const { lower, current, upper } = getPercentageValues(currentIndex);

  const handleIngredientPress = (title: string) => {
    setSelectedIngredients((prev) =>
      prev.includes(title)
        ? prev.filter((item) => item !== title)
        : [...prev, title]
    );
  };

  const handleDeleteSelected = () => {
    if (selectedIngredients.length === 0) return;

    // Start fade out animation for selected ingredients
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      // After animation completes, remove the selected ingredients
      setIngredientsData((prev) =>
        prev.filter((item) => !selectedIngredients.includes(item.title))
      );
      setSelectedIngredients([]);

      // Reset animation
      fadeAnim.setValue(1);
    });
  };

  const renderIngredientList = () => {
    return (
      <FlatList
        data={ingredientsData}
        contentContainerStyle={{ gap: verticalScale(10) }}
        renderItem={({ item }) => {
          const isSelected = selectedIngredients.includes(item.title);
          return (
            <TouchableOpacity
              onPress={() => handleIngredientPress(item.title)}
              activeOpacity={0.7}
              style={[
                styles.ingredientItem,
                isSelected && styles.selectedIngredientItem, // Apply selected style
              ]}
            >
              <Image
                source={{
                  uri: "https://images.unsplash.com/photo-1590779033100-9f60a05a013d?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                }}
                style={styles.ingredientImage}
              />
              <View style={styles.ingredientDetails}>
                <CustomText
                  color={COLORS.yellow}
                  fontFamily="medium"
                  fontSize={12}
                >
                  {item.title}
                </CustomText>
                <CustomText
                  color={COLORS.white}
                  fontFamily="medium"
                  fontSize={12}
                >
                  {item.quantity}
                </CustomText>
                <View style={styles.caloriesContainer}>
                  {Array.from({ length: 4 }).map((_, index) => (
                    <View style={styles.caloriesItem} key={index.toString()}>
                      <CustomText fontSize={10} fontFamily="medium">
                        Calories
                      </CustomText>
                      <CustomText
                        fontSize={12}
                        fontFamily="medium"
                        style={styles.caloriesValue}
                      >
                        1500
                      </CustomText>
                    </View>
                  ))}
                </View>
              </View>
              <View style={styles.percentageControl}>
                <TouchableOpacity onPress={handleDecrease}>
                  <CustomIcon Icon={ICONS.ArrowUpIcon} height={20} width={20} />
                </TouchableOpacity>

                <View style={{ alignItems: "center", gap: verticalScale(5) }}>
                  <CustomText
                    fontSize={10}
                    fontFamily="medium"
                  >{`${lower}%`}</CustomText>
                  <CustomText
                    fontSize={10}
                    fontFamily="medium"
                  >{`${current}%`}</CustomText>
                  <CustomText
                    fontSize={10}
                    fontFamily="medium"
                  >{`${upper}%`}</CustomText>
                </View>
                <TouchableOpacity onPress={handleIncrease}>
                  <CustomIcon
                    Icon={ICONS.ArrowDownIcon}
                    height={20}
                    width={20}
                  />
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          );
        }}
        ListFooterComponent={() => (
          <View style={{ alignItems: "flex-end" }}>
            <PrimaryButton
              onPress={() => {}}
              isFullWidth={false}
              style={styles.addIngredientsButton}
              textSize={10}
              title="Add ingredients"
            />
          </View>
        )}
      />
    );
  };

  return (
    <View style={styles.main}>
      <SafeAreaView edges={["top", "left", "right"]} style={styles.safeArea}>
        <View
          style={{
            flex: 1,
            backgroundColor: COLORS.darkBrown,
            alignItems: "center",
            gap: verticalScale(20),
            paddingTop: verticalScale(10),
          }}
        >
          <View
            style={{
              paddingLeft: 10,
              justifyContent: "flex-start",
              width: "100%",
              paddingTop: verticalScale(10),
            }}
          >
            <CustomIcon
              onPress={() => {
                navigation.goBack();
              }}
              Icon={ICONS.BackArrow}
            />
          </View>
          <CustomText fontFamily="bold">Total Meal Macro</CustomText>
          <View style={styles.mealStatsContainer}>
            {[
              { title: "Calories", value: 1500 },
              { title: "Fat", value: 1500 },
              { title: "Protein", value: 1500 },
              { title: "Carbs", value: 1500 },
            ].map((item, index) => (
              <View
                style={{ alignItems: "center", gap: verticalScale(5) }}
                key={index.toString()}
              >
                <CustomText
                  fontSize={10}
                  fontFamily="medium"
                  color={COLORS.whiteTail}
                >
                  {item.title}
                </CustomText>

                <View key={index.toString()} style={styles.mealStatItem}>
                  <CustomText fontSize={14} fontFamily="medium">
                    {item.value}
                  </CustomText>
                </View>
              </View>
            ))}
          </View>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              borderBottomColor: COLORS.white,
              borderBottomWidth: 1,
              width: wp(100),
              paddingBottom: verticalScale(10),
              paddingHorizontal: horizontalScale(10),
            }}
          >
            {selectedIngredients.length > 0 && (
              <View style={{ flexDirection: "row", gap: horizontalScale(10) }}>
                <TouchableOpacity
                  onPress={handleDeleteSelected}
                  style={styles.actionButton}
                >
                  <CustomIcon Icon={ICONS.DeleteIcon} height={15} width={15} />
                  <CustomText fontSize={7}>Delete</CustomText>
                </TouchableOpacity>
                <TouchableOpacity style={styles.actionButton}>
                  <CustomIcon Icon={ICONS.CopyIcon} height={15} width={15} />
                  <CustomText fontSize={7}>Copy</CustomText>
                </TouchableOpacity>
              </View>
            )}
          </View>
          {renderIngredientList()}
        </View>
        <View style={styles.bottomButtons}>
          <PrimaryButton title="Log Meal" onPress={() => {}} />
          <PrimaryButton
            title="Save Meal"
            onPress={() => {}}
            style={{ backgroundColor: "transparent" }}
          />
        </View>
      </SafeAreaView>
    </View>
  );
};

export default LogMeal;

const styles = StyleSheet.create({
  main: { backgroundColor: COLORS.darkBrown, flex: 1 },
  safeArea: {
    flex: 1,
  },
  mealStatsContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: horizontalScale(10),
  },
  mealStatItem: {
    alignItems: "center",
    backgroundColor: COLORS.lightBrown,
    paddingVertical: verticalScale(8),
    paddingHorizontal: horizontalScale(20),
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.brown,
    gap: verticalScale(5),
  },
  ingredientItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    borderWidth: 1,
    borderRadius: verticalScale(10),
    borderColor: COLORS.whiteTail,
    backgroundColor: COLORS.lightBrown,
    width: wp(95),
  },
  selectedIngredientItem: {
    backgroundColor: COLORS.skinColor,
  },
  ingredientImage: {
    height: "100%",
    width: wp(22),
    borderRadius: 10,
    resizeMode: "cover",
  },
  ingredientDetails: {
    paddingVertical: verticalScale(10),
    paddingHorizontal: horizontalScale(10),
    justifyContent: "space-between",
    flex: 1,
  },
  caloriesContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: horizontalScale(5),
  },
  caloriesItem: {
    alignItems: "center",
  },
  caloriesValue: {
    backgroundColor: COLORS.lighterBrown,
    paddingVertical: verticalScale(2),
    paddingHorizontal: horizontalScale(4),
    borderRadius: 5,
    borderWidth: 1,
    borderColor: COLORS.brown,
  },
  percentageControl: {
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: verticalScale(5),
    paddingHorizontal: horizontalScale(20),
  },
  actionButton: {
    alignItems: "center",
    borderWidth: 1,
    borderColor: COLORS.whiteTail,
    borderRadius: 100,
    justifyContent: "center",
    height: 40,
    width: 40,
  },
  addIngredientsButton: {
    width: "auto",
    paddingVertical: verticalScale(8),
    paddingHorizontal: horizontalScale(12),
    borderRadius: verticalScale(5),
  },
  bottomButtons: {
    backgroundColor: COLORS.brown,
    width: wp(100),
    paddingVertical: verticalScale(10),
  },
});
