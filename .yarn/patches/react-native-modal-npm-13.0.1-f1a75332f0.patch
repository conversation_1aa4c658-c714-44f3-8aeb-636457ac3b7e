diff --git a/dist/modal.js b/dist/modal.js
index 80f4e751b165e9730cc74eae7d3cba9b6aef0f6f..d57a7a8f74e5c3b5fdadb87ba8124d679d6117e0 100644
--- a/dist/modal.js
+++ b/dist/modal.js
@@ -453,10 +453,11 @@ export class ReactNativeModal extends React.Component {
         if (this.state.isVisible) {
             this.open();
         }
-        BackHandler.addEventListener('hardwareBackPress', this.onBackButtonPress);
+       this.backhandler =  BackHandler.addEventListener('hardwareBackPress', this.onBackButtonPress);
     }
     componentWillUnmount() {
-        BackHandler.removeEventListener('hardwareBackPress', this.onBackButtonPress);
+         this.backhandler.remove();
+       
         if (this.didUpdateDimensionsEmitter) {
             this.didUpdateDimensionsEmitter.remove();
         }
