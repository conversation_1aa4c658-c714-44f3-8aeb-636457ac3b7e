import { configureStore } from "@reduxjs/toolkit";
import { useDispatch, useSelector } from "react-redux";
import initialReducer from "./slices/initialSlice";
import modalReducer from "./slices/modalSlice";
import newWorkoutReducer from "./slices/newWorkoutSlice";
import exerciseCatalogReducer from "./slices/exerciseCatalogSlice";
import muscleReducer from "./slices/muscleSlice";
import savedWorkoutsReducer from "./slices/savedWorkoutsSlice";

export const store = configureStore({
  reducer: {
    initial: initialReducer,
    modals: modalReducer,
    newWorkout: newWorkoutReducer,
    exerciseCatalog: exerciseCatalogReducer,
    muscle: muscleReducer,
    savedWorkouts: savedWorkoutsReducer,
  },
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch = useDispatch.withTypes<AppDispatch>();
export const useAppSelector = useSelector.withTypes<RootState>();
