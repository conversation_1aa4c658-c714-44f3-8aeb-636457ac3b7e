import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  FlatList,
  Dimensions,
  TouchableWithoutFeedback,
} from 'react-native';
import { CustomText } from './CustomText';
import COLORS from '../Utilities/Colors';
import { horizontalScale, verticalScale } from '../Utilities/Metrics';
import CustomIcon from './CustomIcon';
import ICONS from '../Assets/Icons';

interface DropdownOption {
  label: string;
  value: string;
}

interface DropdownSelectProps {
  label: string;
  options: DropdownOption[];
  selectedValue: string;
  onSelect: (value: string) => void;
  containerStyle?: object;
}

const DropdownSelect: React.FC<DropdownSelectProps> = ({
  label,
  options,
  selectedValue,
  onSelect,
  containerStyle,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const dropdownButtonRef = useRef<typeof TouchableOpacity>(null);

  // Find the selected option label
  const selectedOption = options.find(option => option.value === selectedValue);

  const openDropdown = () => {
    // Calculate the position of the dropdown
    if (dropdownButtonRef.current) {
      dropdownButtonRef.current.measure((x, y, width, height, pageX, pageY) => {
        const windowHeight = Dimensions.get('window').height;
        const spaceBelow = windowHeight - pageY - height;
        
        // Determine if dropdown should appear below or above the button
        const top = spaceBelow > 200 ? pageY + height : pageY - 150;
        
        setDropdownPosition({
          top,
          left: pageX,
          width,
        });
        setIsVisible(true);
      });
    }
  };

  const closeDropdown = () => {
    setIsVisible(false);
  };

  const handleSelect = (value: string) => {
    onSelect(value);
    closeDropdown();
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <CustomText fontSize={14} fontFamily="medium" color={COLORS.white}>
        {label}
      </CustomText>
      
      <TouchableOpacity
        ref={dropdownButtonRef}
        style={styles.dropdownButton}
        onPress={openDropdown}
      >
        <CustomText 
          fontSize={12} 
          fontFamily="medium" 
          color={COLORS.white}
          style={styles.selectedText}
        >
          {selectedOption?.label || 'Select an option'}
        </CustomText>
        <CustomIcon Icon={ICONS.ArrowDownIcon} height={12} width={12} />
      </TouchableOpacity>

      <Modal
        visible={isVisible}
        transparent
        animationType="fade"
        onRequestClose={closeDropdown}
      >
        <TouchableWithoutFeedback onPress={closeDropdown}>
          <View style={styles.modalOverlay}>
            <View
              style={[
                styles.dropdown,
                {
                  top: dropdownPosition.top,
                  left: dropdownPosition.left,
                  width: dropdownPosition.width,
                },
              ]}
            >
              <FlatList
                data={options}
                keyExtractor={(item) => item.value}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.option,
                      selectedValue === item.value && styles.selectedOption,
                    ]}
                    onPress={() => handleSelect(item.value)}
                  >
                    <CustomText
                      fontSize={12}
                      fontFamily="medium"
                      color={selectedValue === item.value ? COLORS.darkBrown : COLORS.white}
                    >
                      {item.label}
                    </CustomText>
                  </TouchableOpacity>
                )}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: verticalScale(15),
  },
  dropdownButton: {
    backgroundColor: COLORS.lightBrown,
    borderRadius: 8,
    padding: horizontalScale(10),
    marginTop: verticalScale(8),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.whiteTail,
  },
  selectedText: {
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  dropdown: {
    position: 'absolute',
    backgroundColor: COLORS.brown,
    borderRadius: 8,
    maxHeight: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  option: {
    padding: horizontalScale(10),
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightBrown,
  },
  selectedOption: {
    backgroundColor: COLORS.yellow,
  },
});

export default DropdownSelect;
