import React, { FC, useState } from "react";
import {
  FlatList,
  Image,
  ImageBackground,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import LinearGradient from "react-native-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icons";
import CustomIcon from "../../Components/CustomIcon";
import { CustomText } from "../../Components/CustomText";
import PrimaryButton from "../../Components/PrimaryButton";
import { myMealsList } from "../../Seeds/Plans";
import {
  AddNewMealScreenProps,
  MealDetailScreenProps,
} from "../../Typings/route";
import COLORS from "../../Utilities/Colors";
import {
  horizontalScale,
  hp,
  verticalScale,
  wp,
} from "../../Utilities/Metrics";
import UploadImageOptions from "../../Components/Modals/UploadImageOptions";
import { launchCamera, launchImageLibrary } from "react-native-image-picker";

interface CapturedPhoto {
  uri: string;
  width?: number;
  height?: number;
  type?: string;
}

interface Ingredient {
  name: string;
  weight: string;
  image: string;
}

const AddNewMeal: FC<AddNewMealScreenProps> = ({ navigation }) => {
  const insets = useSafeAreaInsets();

  // State for editable fields
  const [title, setTitle] = useState("Chicken with mix potato");
  const [description, setDescription] = useState(
    "Juicy, tender salmon fillet seasoned with a zesty lemon-herb marinade."
  );
  const [prepTime, setPrepTime] = useState("10 min");
  const [difficulty, setDifficulty] = useState("Medium");
  const [calories, setCalories] = useState("512 cal");
  const [instructions, setInstructions] = useState("");
  const [image, setImage] = useState<CapturedPhoto | null>({
    uri: "https://plus.unsplash.com/premium_photo-1661419883163-bb4df1c10109?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fHx8fA%3D%3D",
  });
  const [isUploadImageOptionModal, setIsUploadImageOptionModal] =
    useState(false);
  const [currentImageType, setCurrentImageType] = useState<
    "cover" | "ingredient"
  >("cover");
  const [currentIngredientIndex, setCurrentIngredientIndex] = useState<
    number | null
  >(null);
  const [ingredients, setIngredients] = useState<Ingredient[]>([
    {
      name: "Apple",
      weight: "10 g",
      image: "https://nix-tag-images.s3.amazonaws.com/384_highres.jpg",
    },
  ]);

  const [showInstructionInput, setShowInstructionInput] = useState(false);
  const [showDescriptionInput, setShowDescriptionInput] = useState(false);
  const [showTitleInput, setShowTitleInput] = useState(false);

  const handleSave = () => {
    // Implement save functionality here (e.g., API call or state update)
    navigation.goBack();
  };

  const closeModal = () => {
    setIsUploadImageOptionModal(false);
    setCurrentIngredientIndex(null);
  };

  const openImagePicker = (type: "cover" | "ingredient", index?: number) => {
    setCurrentImageType(type);
    if (type === "ingredient" && index !== undefined) {
      setCurrentIngredientIndex(index);
    }
    setIsUploadImageOptionModal(true);
  };

  const handleImagePick = () => {
    launchImageLibrary({ mediaType: "photo", quality: 0.8 }, (response) => {
      if (response.didCancel) {
        console.log("User cancelled image picker");
      } else if (response.errorCode) {
        console.log("ImagePicker Error: ", response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        const imageData: CapturedPhoto = {
          uri: asset.uri || "",
          width: asset.width,
          height: asset.height,
          type: asset.type,
        };

        if (currentImageType === "cover") {
          setImage(imageData);
        } else if (
          currentImageType === "ingredient" &&
          currentIngredientIndex !== null
        ) {
          // Update the ingredient image
          const updatedIngredients = [...ingredients];
          updatedIngredients[currentIngredientIndex] = {
            ...updatedIngredients[currentIngredientIndex],
            image: imageData.uri,
          };
          setIngredients(updatedIngredients);
        }
      }
      closeModal();
    });
  };

  const handleCameraPick = async () => {
    try {
      const result = await launchCamera({
        quality: 1,
        mediaType: "photo",
      });

      if (result.didCancel) {
        console.log("User cancelled camera");
      } else if (result.errorCode) {
        console.log("Camera error:", result.errorMessage);
      } else if (result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const imageData: CapturedPhoto = {
          uri: asset.uri || "",
          width: asset.width,
          height: asset.height,
          type: asset.type,
        };

        if (currentImageType === "cover") {
          setImage(imageData);
        } else if (
          currentImageType === "ingredient" &&
          currentIngredientIndex !== null
        ) {
          // Update the ingredient image
          const updatedIngredients = [...ingredients];
          updatedIngredients[currentIngredientIndex] = {
            ...updatedIngredients[currentIngredientIndex],
            image: imageData.uri,
          };
          setIngredients(updatedIngredients);
        }
      }

      closeModal();
    } catch (error) {
      console.log("Camera capture failed:", error);
    }
  };

  const handleIngredientChange = (
    index: number,
    field: keyof Ingredient,
    value: string
  ) => {
    const updatedIngredients = [...ingredients];
    updatedIngredients[index] = {
      ...updatedIngredients[index],
      [field]: value,
    };
    setIngredients(updatedIngredients);
  };

  const addIngredient = () => {
    setIngredients([
      ...ingredients,
      {
        name: "",
        weight: "",
        image: "https://via.placeholder.com/150/CCCCCC/808080?text=Add+Image",
      },
    ]);
  };

  const removeIngredient = (index: number) => {
    setIngredients(ingredients.filter((_, i) => i !== index));
  };

  return (
    <View style={styles.contentContainer}>
      {/* Cover Image Section */}
      <ImageBackground
        source={{
          uri: image?.uri,
        }}
        style={styles.coverImage}
        imageStyle={styles.coverImageStyle}
      >
        <LinearGradient
          colors={["rgba(0,0,0,0)", "#1F1A16"]}
          style={styles.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
        >
          <View style={styles.headerContainer}>
            <CustomIcon
              onPress={() => navigation.goBack()}
              Icon={ICONS.BackArrow}
            />
            <View style={styles.headerTextContainer}>
              <View style={styles.imageOverlay}>
                <CustomIcon Icon={ICONS.EditIcon} />
              </View>
            </View>
          </View>
        </LinearGradient>
      </ImageBackground>

      <ScrollView
        contentContainerStyle={{
          gap: verticalScale(40),
        }}
      >
        {/* Title Section */}
        <View style={[styles.section, { paddingHorizontal: 20 }]}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <CustomText fontFamily="extraBold" fontSize={18}>
              Title
            </CustomText>
            <TouchableOpacity
              onPress={() => setShowTitleInput(!showTitleInput)}
            >
              <CustomIcon Icon={ICONS.EditIcon} height={20} width={20} />
            </TouchableOpacity>
          </View>
          {showTitleInput && (
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="Enter meal title"
              placeholderTextColor={COLORS.lightBrown}
            />
          )}
        </View>

        {/* Description Section */}
        <View style={[styles.section, { paddingHorizontal: 20 }]}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <CustomText fontFamily="extraBold" fontSize={18}>
              Description
            </CustomText>
            <TouchableOpacity
              onPress={() => setShowDescriptionInput(!showDescriptionInput)}
            >
              <CustomIcon Icon={ICONS.EditIcon} height={20} width={20} />
            </TouchableOpacity>
          </View>
          {showDescriptionInput && (
            <TextInput
              style={[styles.input, styles.multilineInput]}
              value={description}
              onChangeText={setDescription}
              placeholder="Enter meal description"
              placeholderTextColor={COLORS.lightBrown}
              multiline
              numberOfLines={4}
            />
          )}
        </View>

        {/* Info Section */}
        {/* <View style={styles.section}>
        <CustomText fontFamily="extraBold" fontSize={18}>
          Meal Info
        </CustomText>
        <View style={styles.infoContainer}>
          <View style={styles.infoInputContainer}>
            <CustomText fontFamily="medium">Prep Time</CustomText>
            <TextInput
              style={styles.infoInput}
              value={prepTime}
              onChangeText={setPrepTime}
              placeholder="e.g., 10 min"
              placeholderTextColor={COLORS.lightBrown}
            />
          </View>
          <View style={styles.infoInputContainer}>
            <CustomText fontFamily="medium">Difficulty</CustomText>
            <TextInput
              style={styles.infoInput}
              value={difficulty}
              onChangeText={setDifficulty}
              placeholder="e.g., Medium"
              placeholderTextColor={COLORS.lightBrown}
            />
          </View>
          <View style={styles.infoInputContainer}>
            <CustomText fontFamily="medium">Calories</CustomText>
            <TextInput
              style={styles.infoInput}
              value={calories}
              onChangeText={setCalories}
              placeholder="e.g., 512 cal"
              placeholderTextColor={COLORS.lightBrown}
            />
          </View>
        </View>
      </View> */}

        {/* Ingredients Section */}
        <View style={[styles.section, { paddingHorizontal: 20 }]}>
          <View style={styles.sectionHeader}>
            <CustomText fontFamily="extraBold" fontSize={18}>
              Ingredients
            </CustomText>
            <PrimaryButton
              onPress={addIngredient}
              title="Add +"
              isFullWidth={false}
              style={{
                paddingVertical: verticalScale(5),
                paddingHorizontal: horizontalScale(10),
                borderRadius: verticalScale(5),
              }}
            />
          </View>
          {ingredients.map((ingredient, index) => (
            <View key={index} style={styles.ingredientRow}>
              <TouchableOpacity
                onPress={() => openImagePicker("ingredient", index)}
              >
                <Image
                  source={{ uri: ingredient.image }}
                  style={styles.ingredientImage}
                />
                <View style={styles.ingredientImageOverlay}>
                  <CustomIcon Icon={ICONS.CamearIcon} height={20} width={20} />
                </View>
              </TouchableOpacity>
              <TextInput
                style={[styles.input, styles.ingredientInput]}
                value={ingredient.name}
                onChangeText={(text) =>
                  handleIngredientChange(index, "name", text)
                }
                placeholder="Ingredient name"
                placeholderTextColor={COLORS.lightBrown}
              />
              <TextInput
                style={[styles.input, styles.weightInput]}
                value={ingredient.weight}
                onChangeText={(text) =>
                  handleIngredientChange(index, "weight", text)
                }
                placeholder="Weight"
                placeholderTextColor={COLORS.lightBrown}
              />
              <TouchableOpacity onPress={() => removeIngredient(index)}>
                <CustomIcon Icon={ICONS.DeleteIcon} height={20} width={20} />
              </TouchableOpacity>
            </View>
          ))}
        </View>

        {/* Instructions Section */}
        <View style={[styles.section, { paddingHorizontal: 20 }]}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <CustomText fontFamily="extraBold" fontSize={18}>
              Instructions
            </CustomText>
            <TouchableOpacity
              onPress={() => setShowInstructionInput(!showInstructionInput)}
            >
              <CustomIcon Icon={ICONS.EditIcon} height={20} width={20} />
            </TouchableOpacity>
          </View>
          {showInstructionInput && (
            <TextInput
              style={[styles.input, styles.multilineInput]}
              value={instructions}
              onChangeText={setInstructions}
              placeholder="Enter cooking instructions"
              placeholderTextColor={COLORS.lightBrown}
              multiline
              numberOfLines={6}
            />
          )}
        </View>
      </ScrollView>

      {/* Save Button */}
      <PrimaryButton
        title="Save"
        onPress={handleSave}
        style={styles.saveButton}
      />

      {isUploadImageOptionModal && (
        <UploadImageOptions
          closeModal={closeModal}
          isModalVisible={isUploadImageOptionModal}
          onPressCamera={handleCameraPick}
          onPressGallery={handleImagePick}
          title={
            currentImageType === "cover"
              ? "Select Cover Image"
              : "Select Ingredient Image"
          }
        />
      )}
    </View>
  );
};

export default AddNewMeal;

const styles = StyleSheet.create({
  coverImage: {
    height: hp(20),
  },
  coverImageStyle: {
    borderRadius: 10,
    resizeMode: "cover",
  },
  gradient: {
    flex: 1,
  },
  headerContainer: {
    flex: 1,
    justifyContent: "space-between",
    alignItems: "flex-start",
    gap: verticalScale(10),
    paddingHorizontal: verticalScale(10),
    paddingVertical: verticalScale(10),
  },
  headerTextContainer: {
    gap: verticalScale(10),
  },
  contentContainer: {
    flex: 1,
    backgroundColor: COLORS.darkBrown,
    paddingBottom: verticalScale(30),
    gap: verticalScale(40),
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: horizontalScale(10),
    paddingBottom: verticalScale(10),
  },
  section: {
    gap: verticalScale(10),
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  imageContainer: {
    height: verticalScale(200),
    borderRadius: 10,
    overflow: "hidden",
  },
  imageOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "flex-start",
    alignItems: "flex-end",
    gap: verticalScale(5),
    flexDirection: "row",
    padding: verticalScale(10),
  },
  overlayText: {
    color: COLORS.whiteTail,
    fontFamily: "medium",
  },
  input: {
    backgroundColor: COLORS.brown,
    borderRadius: 8,
    padding: verticalScale(10),
    color: COLORS.whiteTail,
    fontFamily: "medium",
    fontSize: 14,
    borderWidth: 1,
    borderColor: COLORS.lightBrown,
  },
  multilineInput: {
    minHeight: verticalScale(100),
    textAlignVertical: "top",
  },
  infoContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: horizontalScale(10),
  },
  infoInputContainer: {
    flex: 1,
    gap: verticalScale(5),
  },
  infoInput: {
    backgroundColor: COLORS.brown,
    borderRadius: 8,
    padding: verticalScale(8),
    color: COLORS.whiteTail,
    fontFamily: "medium",
    fontSize: 14,
    borderWidth: 1,
    borderColor: COLORS.lightBrown,
    textAlign: "center",
  },
  ingredientRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: horizontalScale(10),
    marginVertical: verticalScale(5),
  },
  ingredientImage: {
    height: hp(8),
    width: hp(8),
    resizeMode: "cover",
    borderRadius: 10,
  },
  ingredientInput: {
    flex: 1,
  },
  weightInput: {
    width: wp(20),
    textAlign: "center",
  },
  saveButton: {
    width: wp(90),
    alignSelf: "center",
    borderRadius: 10,
  },
  ingredientImageOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
  },
});
