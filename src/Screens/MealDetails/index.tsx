import React, { <PERSON> } from "react";
import {
  FlatList,
  Image,
  ImageBackground,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import LinearGradient from "react-native-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import ICONS from "../../Assets/Icons";
import CustomIcon from "../../Components/CustomIcon";
import { CustomText } from "../../Components/CustomText";
import PrimaryButton from "../../Components/PrimaryButton";
import { myMealsList } from "../../Seeds/Plans";
import { MealDetailScreenProps } from "../../Typings/route";
import COLORS from "../../Utilities/Colors";
import {
  horizontalScale,
  hp,
  verticalScale,
  wp,
} from "../../Utilities/Metrics";

const MACROS = [
  { label: "Calories", value: "512 cal" },
  { label: "Fat", value: "12g" },
  { label: "Carbs", value: "9g" },
  { label: "Protein", value: "30g" },
];

const TAGS = Array.from({ length: 7 }, () => "Breakfast");

const INGREDIENTS = Array.from({ length: 3 }, () => ({
  name: "Apple",
  weight: "10 g",
  image: "https://nix-tag-images.s3.amazonaws.com/384_highres.jpg",
}));

const MealDetails: FC<MealDetailScreenProps> = ({ navigation, route }) => {
  const insets = useSafeAreaInsets();
  const { mealId, isFromMyMeal } = route.params;

  const renderBanner = () => (
    <ImageBackground
      source={{
        uri: "https://plus.unsplash.com/premium_photo-1661419883163-bb4df1c10109?q=80&w=1587&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fHx8fA%3D%3D",
      }}
      style={styles.banner}
      imageStyle={{ resizeMode: "cover" }}
    >
      <LinearGradient
        colors={["rgba(0,0,0,0)", "#1F1A16"]}
        style={styles.gradient}
      >
        <View
          style={[
            styles.bannerTextContainer,
            {
              paddingTop: insets.top + verticalScale(5),
            },
          ]}
        >
          <CustomIcon
            onPress={() => navigation.goBack()}
            Icon={ICONS.BackArrow}
          />
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <CustomText
              fontSize={18}
              fontFamily="bold"
              style={styles.bannerText}
            >
              Chicken with mix potato and something To make 2nd line
            </CustomText>
            {isFromMyMeal && (
              <TouchableOpacity
                onPress={() =>
                  navigation.navigate("editMealDetails", { mealId })
                }
                activeOpacity={0.7}
              >
                <CustomIcon Icon={ICONS.EditIcon} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </LinearGradient>
    </ImageBackground>
  );

  const renderMacros = () => (
    <View style={styles.section}>
      <CustomText fontFamily="extraBold" fontSize={24}>
        Macros
      </CustomText>
      <View style={styles.macrosContainer}>
        {MACROS.map(({ label, value }) => (
          <View key={label} style={styles.macroRow}>
            <CustomText fontSize={14} fontFamily="medium">
              {label}
            </CustomText>
            <CustomText fontSize={14} fontFamily="medium">
              {value}
            </CustomText>
          </View>
        ))}
      </View>
    </View>
  );

  const renderMealImages = () => (
    <FlatList
      horizontal
      data={myMealsList}
      keyExtractor={(item) => item.image}
      contentContainerStyle={styles.mealList}
      renderItem={({ item }) => (
        <ImageBackground
          source={{ uri: item.image }}
          imageStyle={{
            borderRadius: 10,
          }}
          style={styles.mealImage}
        >
          <View
            style={{
              justifyContent: "flex-end",
              flex: 1,
              paddingVertical: verticalScale(5),
              paddingHorizontal: horizontalScale(5),
            }}
          >
            <CustomText fontSize={14} fontFamily="medium">
              {item.title}
            </CustomText>
          </View>
        </ImageBackground>
      )}
      showsHorizontalScrollIndicator={false}
    />
  );

  const renderSectionDivider = () => <View style={styles.divider} />;

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      {renderBanner()}

      {/* Info Section */}
      {!isFromMyMeal && (
        <View style={styles.infoRow}>
          {["10 min", "Medium", "512 cal"].map((info) => (
            <CustomText key={info} fontSize={14} fontFamily="medium">
              {info}
            </CustomText>
          ))}
        </View>
      )}

      {/* Tags */}
      {!isFromMyMeal && (
        <View style={styles.tagsContainer}>
          {TAGS.map((tag, index) => (
            <CustomText
              key={index}
              fontSize={14}
              fontFamily="medium"
              color={COLORS.whiteTail}
              style={styles.tag}
            >
              {tag}
            </CustomText>
          ))}
        </View>
      )}

      {renderMacros()}
      {renderSectionDivider()}

      {/* Description */}
      <View style={styles.section}>
        <CustomText fontFamily="extraBold" fontSize={24}>
          Description
        </CustomText>
        <CustomText fontSize={14} style={styles.description}>
          Juicy, tender salmon fillet seasoned with a zesty lemon-herb marinade.
        </CustomText>
      </View>
      {renderSectionDivider()}

      {renderMealImages()}
      {renderSectionDivider()}

      {/* Ingredients */}
      <View style={styles.section}>
        <CustomText fontFamily="extraBold" fontSize={24}>
          Ingredients
        </CustomText>
        <View style={styles.ingredientsContainer}>
          {INGREDIENTS.map(({ name, weight, image }, index) => (
            <View key={index} style={styles.ingredientRow}>
              <Image source={{ uri: image }} style={styles.ingredientImage} />
              <CustomText style={styles.ingredientText}>{name}</CustomText>
              <CustomText>{weight}</CustomText>
            </View>
          ))}
        </View>
      </View>

      {renderSectionDivider()}

      {/* Instructions */}
      <View style={styles.section}>
        <CustomText fontFamily="extraBold" fontSize={24}>
          Instructions
        </CustomText>
        <CustomText fontSize={14} style={styles.description}>
          Cook the oats according to package instructions. Once the oats are
          cooked, stir in the whey protein until fully dissolved.
        </CustomText>
      </View>

      <PrimaryButton title="Log" onPress={() => {}} style={styles.button} />
    </ScrollView>
  );
};

export default MealDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.darkBrown,
  },
  contentContainer: {
    gap: verticalScale(30),
    paddingBottom: verticalScale(20),
  },
  banner: {
    height: hp(45),
    justifyContent: "flex-end",
  },
  gradient: {
    flex: 1,
    justifyContent: "flex-end",
  },
  bannerTextContainer: {
    flex: 1,
    justifyContent: "space-between",
    gap: verticalScale(10),
    paddingBottom: verticalScale(50),
    paddingHorizontal: horizontalScale(10),
  },
  bannerText: {
    width: "85%",
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-evenly",
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    rowGap: verticalScale(10),
    columnGap: horizontalScale(10),
    justifyContent: "center",
  },
  tag: {
    padding: verticalScale(5),
    backgroundColor: COLORS.brown,
    borderWidth: 1,
    borderColor: COLORS.lightBrown,
    borderRadius: 5,
  },
  section: {
    gap: verticalScale(10),
    paddingHorizontal: horizontalScale(10),
  },
  macrosContainer: {
    paddingRight: horizontalScale(20),
    gap: verticalScale(10),
  },
  macroRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  divider: {
    height: 0.4,
    backgroundColor: COLORS.lightBrown,
    width: "95%",
    alignSelf: "center",
  },
  description: {
    lineHeight: 22,
  },
  mealList: {
    gap: horizontalScale(15),
    paddingHorizontal: horizontalScale(20),
  },
  mealImage: {
    height: hp(18),
    width: wp(35),
    resizeMode: "cover",
    borderRadius: 10,
  },
  ingredientsContainer: {
    gap: verticalScale(10),
  },
  ingredientRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: horizontalScale(10),
    paddingRight: horizontalScale(20),
  },
  ingredientImage: {
    height: hp(8),
    width: hp(8),
    resizeMode: "cover",
    borderRadius: 10,
  },
  ingredientText: {
    flex: 1,
  },
  button: {
    width: wp(30),
    paddingHorizontal: horizontalScale(20),
    borderRadius: 10,
  },
});
